import prisma from "@/lib/prisma";
import { DepreciationEngine } from "@/lib/engines/depreciation-engine";
import { 
  AssetDepreciationSummary,
  DepreciationReportFilter,
  DepreciationCalculationResult,
  BulkDepreciationCalculation,
  DepreciationAdjustment 
} from "@/lib/schemas/depreciation";
import { Asset, AssetType, DepreciationSettings, DepreciationSchedule } from "@prisma/client";

export interface AssetWithDepreciation extends Asset {
  assetType: (AssetType & {
    depreciationSettings: DepreciationSettings | null;
  }) | null;
  depreciationSchedule: DepreciationSchedule[];
}

export class DepreciationService {
  /**
   * Get assets with depreciation data
   */
  static async getAssetsWithDepreciation(
    filter: DepreciationReportFilter = {},
    pagination: { page: number; limit: number; sortBy?: string; sortOrder?: 'asc' | 'desc' } = { page: 1, limit: 10 }
  ) {
    const where: any = {};

    // Apply filters
    if (filter.assetIds?.length) {
      where.id = { in: filter.assetIds };
    }

    if (filter.assetTypeIds?.length) {
      where.assetTypeId = { in: filter.assetTypeIds };
    }

    if (filter.categories?.length) {
      where.category = { in: filter.categories };
    }

    if (filter.departments?.length) {
      where.department = { in: filter.departments };
    }

    if (filter.locations?.length) {
      where.location = { in: filter.locations };
    }

    if (!filter.includeDisposed) {
      where.status = { not: "disposed" };
    }

    if (filter.dateFrom || filter.dateTo) {
      where.purchaseDate = {};
      if (filter.dateFrom) {
        where.purchaseDate.gte = filter.dateFrom;
      }
      if (filter.dateTo) {
        where.purchaseDate.lte = filter.dateTo;
      }
    }

    if (filter.methods?.length) {
      where.assetType = {
        depreciationSettings: {
          method: { in: filter.methods }
        }
      };
    }

    // Get total count
    const total = await prisma.asset.count({ where });

    // Calculate pagination
    const totalPages = Math.ceil(total / pagination.limit);
    const skip = (pagination.page - 1) * pagination.limit;

    // Fetch assets
    const assets = await prisma.asset.findMany({
      where,
      include: {
        assetType: {
          include: {
            depreciationSettings: true,
          },
        },
        depreciationSchedule: {
          orderBy: [
            { year: 'asc' },
            { month: 'asc' }
          ],
        },
      },
      orderBy: {
        [pagination.sortBy || 'createdAt']: pagination.sortOrder || 'desc',
      },
      skip,
      take: pagination.limit,
    }) as AssetWithDepreciation[];

    return {
      assets,
      total,
      totalPages,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        hasNext: pagination.page < totalPages,
        hasPrev: pagination.page > 1,
      },
    };
  }

  /**
   * Get depreciation summary for an asset
   */
  static async getAssetDepreciationSummary(assetId: string): Promise<AssetDepreciationSummary | null> {
    const asset = await prisma.asset.findUnique({
      where: { id: assetId },
      include: {
        assetType: {
          include: {
            depreciationSettings: true,
          },
        },
        depreciationSchedule: {
          orderBy: [
            { year: 'desc' },
            { month: 'desc' }
          ],
          take: 1,
        },
      },
    });

    if (!asset || !asset.assetType?.depreciationSettings) {
      return null;
    }

    const settings = asset.assetType.depreciationSettings;
    const latestSchedule = asset.depreciationSchedule[0];
    
    // Calculate current values
    const currentBookValue = latestSchedule?.bookValue ?? asset.purchasePrice;
    const accumulatedDepreciation = asset.purchasePrice - currentBookValue;

    // Calculate salvage value
    const salvageValue = settings.salvageValueType === 'percentage' 
      ? asset.purchasePrice * (settings.salvageValue / 100)
      : settings.salvageValue;
    
    const remainingDepreciableAmount = Math.max(0, currentBookValue - salvageValue);

    // Calculate remaining useful life
    const purchaseDate = new Date(asset.purchaseDate);
    const currentDate = new Date();
    const elapsedYears = (currentDate.getTime() - purchaseDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000);
    const remainingUsefulLife = Math.max(0, settings.usefulLife - elapsedYears);

    // Calculate next depreciation date
    const nextDepreciationDate = new Date(currentDate);
    nextDepreciationDate.setMonth(nextDepreciationDate.getMonth() + 1, 1);

    return {
      assetId: asset.id,
      assetName: asset.name,
      purchasePrice: asset.purchasePrice,
      currentBookValue,
      accumulatedDepreciation,
      remainingDepreciableAmount,
      depreciationMethod: settings.method as any,
      usefulLife: settings.usefulLife,
      remainingUsefulLife,
      salvageValue,
      lastCalculatedAt: latestSchedule?.calculatedAt ?? asset.createdAt,
      nextDepreciationDate: remainingUsefulLife > 0 ? nextDepreciationDate : undefined,
    };
  }

  /**
   * Calculate depreciation for an asset
   */
  static async calculateAssetDepreciation(
    assetId: string, 
    recalculate: boolean = false
  ): Promise<DepreciationCalculationResult> {
    const asset = await prisma.asset.findUnique({
      where: { id: assetId },
      include: {
        assetType: {
          include: {
            depreciationSettings: true,
          },
        },
      },
    });

    if (!asset) {
      throw new Error("Asset not found");
    }

    if (!asset.assetType?.depreciationSettings) {
      throw new Error("No depreciation settings found for this asset type");
    }

    // Calculate depreciation using the engine
    const result = await DepreciationEngine.calculateDepreciation({
      assetId,
      purchasePrice: asset.purchasePrice,
      purchaseDate: asset.purchaseDate,
      settings: asset.assetType.depreciationSettings,
    });

    // Save schedule if requested or if no existing schedule
    if (recalculate) {
      await DepreciationEngine.saveDepreciationSchedule(assetId, result.schedule);
    } else {
      const existingSchedule = await prisma.depreciationSchedule.findFirst({
        where: { assetId },
      });
      
      if (!existingSchedule) {
        await DepreciationEngine.saveDepreciationSchedule(assetId, result.schedule);
      }
    }

    return result;
  }

  /**
   * Bulk calculate depreciation for multiple assets
   */
  static async bulkCalculateDepreciation(request: BulkDepreciationCalculation) {
    const results = [];
    const errors = [];

    for (const assetId of request.assetIds) {
      try {
        const result = await this.calculateAssetDepreciation(assetId, request.recalculate);
        results.push({
          assetId,
          success: true,
          data: result,
        });
      } catch (error) {
        errors.push({
          assetId,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return {
      results,
      errors,
      summary: {
        total: request.assetIds.length,
        successful: results.length,
        failed: errors.length,
      },
    };
  }

  /**
   * Get current book value for an asset
   */
  static async getCurrentBookValue(assetId: string): Promise<number> {
    return DepreciationEngine.getCurrentBookValue(assetId);
  }

  /**
   * Get depreciation schedule for an asset
   */
  static async getDepreciationSchedule(assetId: string) {
    return prisma.depreciationSchedule.findMany({
      where: { assetId },
      orderBy: [
        { year: 'asc' },
        { month: 'asc' }
      ],
    });
  }

  /**
   * Get depreciation statistics
   */
  static async getDepreciationStatistics(filter: DepreciationReportFilter = {}) {
    const where: any = {};

    // Apply basic filters
    if (filter.assetIds?.length) {
      where.id = { in: filter.assetIds };
    }

    if (!filter.includeDisposed) {
      where.status = { not: "disposed" };
    }

    // Get assets with depreciation data
    const assets = await prisma.asset.findMany({
      where,
      include: {
        assetType: {
          include: {
            depreciationSettings: true,
          },
        },
        depreciationSchedule: {
          orderBy: [
            { year: 'desc' },
            { month: 'desc' }
          ],
          take: 1,
        },
      },
    });

    // Calculate statistics
    const totalAssets = assets.length;
    const assetsWithDepreciation = assets.filter(a => a.assetType?.depreciationSettings);
    const totalAcquisitionCost = assets.reduce((sum, asset) => sum + asset.purchasePrice, 0);
    
    let totalAccumulatedDepreciation = 0;
    let totalNetBookValue = 0;
    let fullyDepreciatedAssets = 0;

    for (const asset of assetsWithDepreciation) {
      const latestSchedule = asset.depreciationSchedule[0];
      const currentBookValue = latestSchedule?.bookValue ?? asset.purchasePrice;
      const accumulatedDepreciation = asset.purchasePrice - currentBookValue;
      
      totalAccumulatedDepreciation += accumulatedDepreciation;
      totalNetBookValue += currentBookValue;
      
      if (currentBookValue <= (asset.assetType?.depreciationSettings?.salvageValue ?? 0)) {
        fullyDepreciatedAssets++;
      }
    }

    return {
      totalAssets,
      assetsWithDepreciation: assetsWithDepreciation.length,
      totalAcquisitionCost,
      totalAccumulatedDepreciation,
      totalNetBookValue,
      fullyDepreciatedAssets,
      averageDepreciationRate: totalAcquisitionCost > 0 
        ? (totalAccumulatedDepreciation / totalAcquisitionCost) * 100 
        : 0,
    };
  }
}
