import { z } from "zod";

// Base validation schemas aligned with Prisma models

// User validation schemas
export const UserSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  department: z.string().optional(),
  role: z.enum(["user", "admin", "manager", "client"]).default("user"),
  status: z.enum(["active", "inactive", "suspended", "pending_verification"]).default("active"),
  joinDate: z.date().default(() => new Date()),
  lastActive: z.date().default(() => new Date()),
  avatarUrl: z.string().url().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  company: z.string().optional(),
  jobTitle: z.string().optional(),
  emailVerified: z.date().optional(),
  emailVerifyToken: z.string().optional(),
  passwordResetToken: z.string().optional(),
  passwordResetExpiresAt: z.date().optional(),
  twoFactorEnabled: z.boolean().default(false),
  twoFactorSecret: z.string().optional(),
  clientId: z.string().optional(),
  billingAddress: z.record(z.any()).optional(),
  paymentMethods: z.record(z.any()).optional(),
  preferences: z.record(z.any()).optional(),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const UserCreateSchema = UserSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  joinDate: true,
  lastActive: true,
});

export const UserUpdateSchema = UserCreateSchema.partial();

// Asset validation schemas - Enhanced for depreciation
export const AssetSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1, "Asset name is required").max(255, "Asset name too long"),
  category: z.string().min(1, "Category is required").max(100, "Category too long"),
  purchaseDate: z.date(),
  purchasePrice: z.number().positive("Purchase price must be positive"),
  location: z.string().min(1, "Location is required").max(255, "Location too long"),
  department: z.string().max(100, "Department too long").optional(),
  status: z.enum(["active", "maintenance", "disposed"]).default("active"),
  serialNumber: z.string().max(100, "Serial number too long").optional(),
  assetImages: z.array(z.string().url()).default([]),
  assetTypeId: z.string().cuid("Invalid asset type ID").optional(),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

// Asset Type validation schemas
export const AssetTypeSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1, "Asset type name is required").max(255, "Name too long"),
  code: z.string().min(1, "Asset type code is required").max(50, "Code too long"),
  description: z.string().min(1, "Description is required").max(1000, "Description too long"),
  categoryId: z.string().cuid("Invalid category ID"),
  subcategory: z.string().max(100, "Subcategory too long").optional(),
  icon: z.string().min(1, "Icon is required").max(100, "Icon name too long"),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, "Invalid color format").default("#000000"),
  isActive: z.boolean().default(true),
  tags: z.array(z.string().max(50, "Tag too long")).default([]),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
  createdBy: z.string().cuid("Invalid creator ID"),
  version: z.number().int().positive().default(1),
});

// Depreciation Settings validation schemas
export const DepreciationSettingsSchema = z.object({
  id: z.string().cuid(),
  assetTypeId: z.string().cuid("Invalid asset type ID"),
  method: z.enum([
    "straight_line",
    "declining_balance",
    "double_declining_balance",
    "sum_of_years_digits",
    "units_of_production",
    "custom"
  ]),
  usefulLife: z.number().int().positive("Useful life must be positive"),
  usefulLifeUnit: z.enum(["years", "months", "hours", "cycles"]).default("years"),
  salvageValue: z.number().nonnegative("Salvage value cannot be negative"),
  salvageValueType: z.enum(["fixed", "percentage"]).default("percentage"),
  startDate: z.date(),
  customRates: z.string().optional(), // JSON string of DepreciationRate[]
  acceleratedDepreciation: z.string().optional(), // JSON string
  impairmentSettings: z.string().optional(), // JSON string
  isActive: z.boolean().default(true),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

// Depreciation Schedule validation schemas
export const DepreciationScheduleSchema = z.object({
  id: z.string().cuid(),
  assetId: z.string().cuid("Invalid asset ID"),
  year: z.number().int().positive("Year must be positive"),
  month: z.number().int().min(1, "Month must be 1-12").max(12, "Month must be 1-12"),
  depreciationAmount: z.number().nonnegative("Depreciation amount cannot be negative"),
  accumulatedDepreciation: z.number().nonnegative("Accumulated depreciation cannot be negative"),
  bookValue: z.number().nonnegative("Book value cannot be negative"),
  method: z.string().min(1, "Method is required"),
  calculatedAt: z.date().default(() => new Date()),
  isActual: z.boolean().default(false),
  notes: z.string().max(500, "Notes too long").optional(),
});

// Asset Create/Update schemas
export const AssetCreateSchema = AssetSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const AssetUpdateSchema = AssetCreateSchema.partial();

// Asset Type Create/Update schemas
export const AssetTypeCreateSchema = AssetTypeSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  version: true,
});

export const AssetTypeUpdateSchema = AssetTypeCreateSchema.partial();

// Depreciation Settings Create/Update schemas
export const DepreciationSettingsCreateSchema = DepreciationSettingsSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const DepreciationSettingsUpdateSchema = DepreciationSettingsCreateSchema.partial();

// Depreciation Schedule Create/Update schemas
export const DepreciationScheduleCreateSchema = DepreciationScheduleSchema.omit({
  id: true,
  calculatedAt: true,
});

export const DepreciationScheduleUpdateSchema = DepreciationScheduleCreateSchema.partial();

// Asset with relations schema for depreciation calculations
export const AssetWithDepreciationSchema = AssetSchema.extend({
  assetType: AssetTypeSchema.extend({
    depreciationSettings: DepreciationSettingsSchema.nullable(),
  }).nullable(),
  depreciationSchedule: z.array(DepreciationScheduleSchema).default([]),
});

// Depreciation calculation input schema
export const DepreciationCalculationInputSchema = z.object({
  assetId: z.string().cuid("Invalid asset ID"),
  purchasePrice: z.number().positive("Purchase price must be positive"),
  purchaseDate: z.date(),
  currentDate: z.date().optional(),
  recalculate: z.boolean().default(false),
});

// Depreciation calculation result schema
export const DepreciationCalculationResultSchema = z.object({
  totalDepreciation: z.number().nonnegative(),
  remainingValue: z.number().nonnegative(),
  schedule: z.array(z.object({
    year: z.number().int().positive(),
    month: z.number().int().min(1).max(12),
    depreciationAmount: z.number().nonnegative(),
    accumulatedDepreciation: z.number().nonnegative(),
    bookValue: z.number().nonnegative(),
    method: z.string(),
    isActual: z.boolean(),
  })),
  metadata: z.object({
    method: z.string(),
    usefulLife: z.number().positive(),
    salvageValue: z.number().nonnegative(),
    calculatedAt: z.date(),
  }),
});

// Asset Type validation schemas
export const AssetTypeSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1, "Asset type name is required"),
  code: z.string().min(1, "Asset type code is required"),
  description: z.string().min(1, "Description is required"),
  categoryId: z.string().cuid("Invalid category ID"),
  subcategory: z.string().optional(),
  icon: z.string().min(1, "Icon is required"),
  color: z.string().min(1, "Color is required"),
  isActive: z.boolean().default(true),
  tags: z.array(z.string()).default([]),
  createdBy: z.string().cuid("Invalid user ID"),
  version: z.number().int().positive().default(1),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const AssetTypeCreateSchema = AssetTypeSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const AssetTypeUpdateSchema = AssetTypeCreateSchema.partial();

// Asset Request validation schemas
export const AssetRequestSchema = z.object({
  id: z.string().cuid(),
  requestNumber: z.string().min(1, "Request number is required"),
  userId: z.string().cuid("Invalid user ID"),
  assetTypeId: z.string().cuid().optional(),
  assetName: z.string().min(1, "Asset name is required"),
  quantity: z.number().int().positive().default(1),
  priority: z.enum(["low", "normal", "high", "critical"]).default("normal"),
  status: z.enum(["pending", "approved", "rejected", "processing", "shipped", "delivered", "cancelled"]).default("pending"),
  justification: z.string().min(1, "Justification is required"),
  businessCase: z.string().optional(),
  specifications: z.string().optional(),
  estimatedCost: z.number().positive().optional(),
  actualCost: z.number().positive().optional(),
  budgetCode: z.string().optional(),
  department: z.string().optional(),
  location: z.string().min(1, "Location is required"),
  expectedDelivery: z.date().optional(),
  actualDelivery: z.date().optional(),
  approvedBy: z.string().optional(),
  approvedAt: z.date().optional(),
  rejectionReason: z.string().optional(),
  trackingNumber: z.string().optional(),
  notes: z.string().optional(),
  attachments: z.array(z.string()).default([]),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const AssetRequestCreateSchema = AssetRequestSchema.omit({
  id: true,
  requestNumber: true,
  createdAt: true,
  updatedAt: true,
});

export const AssetRequestUpdateSchema = AssetRequestCreateSchema.partial();

// Support Ticket validation schemas
export const SupportTicketSchema = z.object({
  id: z.string().cuid(),
  ticketNumber: z.string().min(1, "Ticket number is required"),
  userId: z.string().cuid("Invalid user ID"),
  subject: z.string().min(1, "Subject is required"),
  description: z.string().min(1, "Description is required"),
  category: z.string().min(1, "Category is required"),
  priority: z.enum(["low", "normal", "high", "critical"]).default("normal"),
  status: z.enum(["open", "in_progress", "waiting_response", "resolved", "closed"]).default("open"),
  assignedTo: z.string().optional(),
  resolution: z.string().optional(),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const SupportTicketCreateSchema = SupportTicketSchema.omit({
  id: true,
  ticketNumber: true,
  createdAt: true,
  updatedAt: true,
});

export const SupportTicketUpdateSchema = SupportTicketCreateSchema.partial();

// Support Message validation schemas
export const SupportMessageSchema = z.object({
  id: z.string().cuid(),
  ticketId: z.string().cuid("Invalid ticket ID"),
  content: z.string().min(1, "Message content is required"),
  sender: z.string().min(1, "Sender is required"),
  senderType: z.enum(["client", "support", "system"]),
  attachments: z.array(z.string()).default([]),
  createdAt: z.date().default(() => new Date()),
});

export const SupportMessageCreateSchema = SupportMessageSchema.omit({
  id: true,
  createdAt: true,
});

// Invoice validation schemas
export const InvoiceSchema = z.object({
  id: z.string().cuid(),
  invoiceNumber: z.string().min(1, "Invoice number is required"),
  userId: z.string().cuid("Invalid user ID"),
  amount: z.number().positive("Amount must be positive"),
  tax: z.number().nonnegative().default(0),
  total: z.number().positive("Total must be positive"),
  currency: z.string().default("USD"),
  status: z.enum(["pending", "paid", "overdue", "cancelled"]).default("pending"),
  dueDate: z.date(),
  paidDate: z.date().optional(),
  paymentMethod: z.string().optional(),
  description: z.string().optional(),
  items: z.record(z.any()),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const InvoiceCreateSchema = InvoiceSchema.omit({
  id: true,
  invoiceNumber: true,
  createdAt: true,
  updatedAt: true,
});

export const InvoiceUpdateSchema = InvoiceCreateSchema.partial();

// Maintenance Task validation schemas
export const MaintenanceTaskSchema = z.object({
  id: z.string().cuid(),
  assetId: z.string().cuid("Invalid asset ID"),
  scheduleId: z.string().cuid().optional(),
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  type: z.string().min(1, "Type is required"),
  priority: z.enum(["low", "medium", "high", "critical"]),
  status: z.enum(["scheduled", "in_progress", "completed", "cancelled", "overdue"]).default("scheduled"),
  scheduledDate: z.date(),
  dueDate: z.date(),
  completedDate: z.date().optional(),
  assignedTo: z.string().optional(),
  estimatedDuration: z.number().int().positive().optional(),
  actualDuration: z.number().int().positive().optional(),
  estimatedCost: z.number().positive().optional(),
  actualCost: z.number().positive().optional(),
  instructions: z.string().optional(),
  checklistItems: z.record(z.any()).optional(),
  completionNotes: z.string().optional(),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const MaintenanceTaskCreateSchema = MaintenanceTaskSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const MaintenanceTaskUpdateSchema = MaintenanceTaskCreateSchema.partial();

// Requisition validation schemas
export const RequisitionSchema = z.object({
  id: z.string().cuid(),
  requestorId: z.string().cuid("Invalid requestor ID"),
  requestorName: z.string().min(1, "Requestor name is required"),
  assetTypeId: z.string().cuid("Invalid asset type ID"),
  quantity: z.number().int().positive(),
  priority: z.enum(["low", "normal", "high", "critical"]).default("normal"),
  justification: z.string().optional(),
  businessCase: z.string().optional(),
  location: z.string().optional(),
  department: z.string().optional(),
  budgetCode: z.string().optional(),
  expectedDelivery: z.date().optional(),
  status: z.enum(["pending", "approved", "rejected", "fulfilled", "cancelled", "partially_fulfilled"]).default("pending"),
  data: z.record(z.any()),
  approvalHistory: z.record(z.any()).optional(),
  fulfillmentData: z.record(z.any()).optional(),
  approvedAt: z.date().optional(),
  approvedBy: z.string().optional(),
  rejectedAt: z.date().optional(),
  rejectedBy: z.string().optional(),
  rejectionReason: z.string().optional(),
  fulfilledAt: z.date().optional(),
  fulfilledBy: z.string().optional(),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const RequisitionCreateSchema = RequisitionSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const RequisitionUpdateSchema = RequisitionCreateSchema.partial();

// Export TypeScript types from schemas
export type User = z.infer<typeof UserSchema>;
export type UserCreate = z.infer<typeof UserCreateSchema>;
export type UserUpdate = z.infer<typeof UserUpdateSchema>;

export type Asset = z.infer<typeof AssetSchema>;
export type AssetCreate = z.infer<typeof AssetCreateSchema>;
export type AssetUpdate = z.infer<typeof AssetUpdateSchema>;

export type AssetType = z.infer<typeof AssetTypeSchema>;
export type AssetTypeCreate = z.infer<typeof AssetTypeCreateSchema>;
export type AssetTypeUpdate = z.infer<typeof AssetTypeUpdateSchema>;

export type AssetRequest = z.infer<typeof AssetRequestSchema>;
export type AssetRequestCreate = z.infer<typeof AssetRequestCreateSchema>;
export type AssetRequestUpdate = z.infer<typeof AssetRequestUpdateSchema>;

export type SupportTicket = z.infer<typeof SupportTicketSchema>;
export type SupportTicketCreate = z.infer<typeof SupportTicketCreateSchema>;
export type SupportTicketUpdate = z.infer<typeof SupportTicketUpdateSchema>;

export type SupportMessage = z.infer<typeof SupportMessageSchema>;
export type SupportMessageCreate = z.infer<typeof SupportMessageCreateSchema>;

export type Invoice = z.infer<typeof InvoiceSchema>;
export type InvoiceCreate = z.infer<typeof InvoiceCreateSchema>;
export type InvoiceUpdate = z.infer<typeof InvoiceUpdateSchema>;

export type MaintenanceTask = z.infer<typeof MaintenanceTaskSchema>;
export type MaintenanceTaskCreate = z.infer<typeof MaintenanceTaskCreateSchema>;
export type MaintenanceTaskUpdate = z.infer<typeof MaintenanceTaskUpdateSchema>;

export type Requisition = z.infer<typeof RequisitionSchema>;
export type RequisitionCreate = z.infer<typeof RequisitionCreateSchema>;
export type RequisitionUpdate = z.infer<typeof RequisitionUpdateSchema>;

// Lease Agreement validation schemas
export const LeaseAgreementSchema = z.object({
  id: z.string().cuid(),
  assetId: z.string().cuid("Invalid asset ID"),
  lessorId: z.string().cuid("Invalid lessor ID"),
  lessorName: z.string().min(1, "Lessor name is required"),
  lesseeId: z.string().cuid("Invalid lessee ID"),
  lesseeName: z.string().min(1, "Lessee name is required"),
  leaseType: z.enum(["Operating", "Finance", "Capital"]),
  startDate: z.date(),
  endDate: z.date(),
  monthlyPayment: z.number().positive("Monthly payment must be positive"),
  totalValue: z.number().positive("Total value must be positive"),
  securityDeposit: z.number().nonnegative(),
  status: z.enum(["Draft", "Active", "Expired", "Terminated", "Renewed"]).default("Draft"),
  renewalOptions: z.number().int().nonnegative().default(0),
  earlyTerminationClause: z.boolean().default(false),
  maintenanceResponsibility: z.enum(["Lessor", "Lessee", "Shared"]),
  insuranceRequirement: z.boolean().default(false),
  terms: z.string().optional(),
  attachments: z.string().optional(),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const LeaseAgreementCreateSchema = LeaseAgreementSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const LeaseAgreementUpdateSchema = LeaseAgreementCreateSchema.partial();

// Asset Category validation schemas
export const AssetCategorySchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1, "Category name is required"),
  description: z.string().min(1, "Description is required"),
  parentId: z.string().cuid().optional(),
  level: z.number().int().nonnegative(),
  path: z.string().min(1, "Path is required"),
  isActive: z.boolean().default(true),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const AssetCategoryCreateSchema = AssetCategorySchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const AssetCategoryUpdateSchema = AssetCategoryCreateSchema.partial();

// Custom Field validation schemas
export const CustomFieldSchema = z.object({
  id: z.string().cuid(),
  assetTypeId: z.string().cuid("Invalid asset type ID"),
  name: z.string().min(1, "Field name is required"),
  label: z.string().min(1, "Field label is required"),
  type: z.string().min(1, "Field type is required"),
  description: z.string().optional(),
  isRequired: z.boolean().default(false),
  isUnique: z.boolean().default(false),
  defaultValue: z.string().optional(),
  validation: z.string().optional(),
  options: z.string().optional(),
  conditionalLogic: z.string().optional(),
  displayOrder: z.number().int().nonnegative(),
  groupName: z.string().optional(),
  isActive: z.boolean().default(true),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const CustomFieldCreateSchema = CustomFieldSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const CustomFieldUpdateSchema = CustomFieldCreateSchema.partial();

// Workflow validation schemas
export const WorkflowSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1, "Workflow name is required"),
  description: z.string().optional(),
  type: z.string().min(1, "Workflow type is required"),
  nodes: z.record(z.any()),
  edges: z.record(z.any()),
  webhooks: z.record(z.any()).optional(),
  createdBy: z.string().optional(),
  isActive: z.boolean().default(true),
  tags: z.array(z.string()).default([]),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const WorkflowCreateSchema = WorkflowSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const WorkflowUpdateSchema = WorkflowCreateSchema.partial();

// Client Profile validation schemas
export const ClientProfileSchema = z.object({
  id: z.string().cuid(),
  userId: z.string().cuid("Invalid user ID"),
  companyName: z.string().optional(),
  industry: z.string().optional(),
  companySize: z.enum(["1-10", "11-50", "51-200", "201-500", "500+"]).optional(),
  website: z.string().url().optional(),
  taxId: z.string().optional(),
  billingContact: z.record(z.any()).optional(),
  technicalContact: z.record(z.any()).optional(),
  accountManager: z.string().optional(),
  subscriptionTier: z.enum(["basic", "professional", "enterprise"]).default("basic"),
  monthlySpend: z.number().nonnegative().default(0),
  creditLimit: z.number().positive().optional(),
  paymentTerms: z.enum(["net_15", "net_30", "net_60"]).default("net_30"),
  autoApprovalLimit: z.number().nonnegative().default(0),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const ClientProfileCreateSchema = ClientProfileSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const ClientProfileUpdateSchema = ClientProfileCreateSchema.partial();

// Export additional TypeScript types
export type LeaseAgreement = z.infer<typeof LeaseAgreementSchema>;
export type LeaseAgreementCreate = z.infer<typeof LeaseAgreementCreateSchema>;
export type LeaseAgreementUpdate = z.infer<typeof LeaseAgreementUpdateSchema>;

export type AssetCategory = z.infer<typeof AssetCategorySchema>;
export type AssetCategoryCreate = z.infer<typeof AssetCategoryCreateSchema>;
export type AssetCategoryUpdate = z.infer<typeof AssetCategoryUpdateSchema>;

export type CustomField = z.infer<typeof CustomFieldSchema>;
export type CustomFieldCreate = z.infer<typeof CustomFieldCreateSchema>;
export type CustomFieldUpdate = z.infer<typeof CustomFieldUpdateSchema>;

export type Workflow = z.infer<typeof WorkflowSchema>;
export type WorkflowCreate = z.infer<typeof WorkflowCreateSchema>;
export type WorkflowUpdate = z.infer<typeof WorkflowUpdateSchema>;

export type ClientProfile = z.infer<typeof ClientProfileSchema>;
export type ClientProfileCreate = z.infer<typeof ClientProfileCreateSchema>;
export type ClientProfileUpdate = z.infer<typeof ClientProfileUpdateSchema>;
