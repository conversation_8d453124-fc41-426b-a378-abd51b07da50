import { NextRequest, NextResponse } from "next/server";
import { DepreciationEngine } from "@/lib/engines/depreciation-engine";
import {
  CalculateDepreciationRequestSchema,
  CalculateDepreciationResponseSchema,
  BulkDepreciationCalculationSchema
} from "@/lib/schemas/depreciation";
import prisma from "@/lib/prisma";
import { z } from "zod";

// POST /api/depreciation/calculate - Calculate depreciation for an asset or multiple assets
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Check if this is a bulk calculation request
    if (body.assetIds && Array.isArray(body.assetIds)) {
      return handleBulkCalculation(body);
    }

    // Validate single asset calculation request
    const { assetId, recalculate } = CalculateDepreciationRequestSchema.parse(body);

    return handleSingleCalculation(assetId, recalculate);

  } catch (error) {
    console.error("Error in depreciation calculation:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid request parameters",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

async function handleSingleCalculation(assetId: string, recalculate: boolean) {
  try {
    // Get asset with depreciation settings
    const asset = await prisma.asset.findUnique({
      where: { id: assetId },
      include: {
        assetType: {
          include: {
            depreciationSettings: true,
          },
        },
      },
    });

    if (!asset) {
      return NextResponse.json(
        {
          success: false,
          error: "Asset not found"
        },
        { status: 404 }
      );
    }

    if (!asset.assetType?.depreciationSettings) {
      return NextResponse.json(
        {
          success: false,
          error: "No depreciation settings found for this asset type"
        },
        { status: 400 }
      );
    }

    // Calculate depreciation
    const result = await DepreciationEngine.calculateDepreciation({
      assetId,
      purchasePrice: asset.purchasePrice,
      purchaseDate: asset.purchaseDate,
      settings: asset.assetType.depreciationSettings,
    });

    // Save schedule if requested or if no existing schedule
    if (recalculate) {
      await DepreciationEngine.saveDepreciationSchedule(assetId, result.schedule);
    } else {
      const existingSchedule = await prisma.depreciationSchedule.findFirst({
        where: { assetId },
      });

      if (!existingSchedule) {
        await DepreciationEngine.saveDepreciationSchedule(assetId, result.schedule);
      }
    }

    return NextResponse.json({
      success: true,
      data: result,
    });

  } catch (error) {
    console.error("Error calculating depreciation:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Internal server error"
      },
      { status: 500 }
    );
  }
}

async function handleBulkCalculation(body: any) {
  try {
    const { assetIds, recalculate, effectiveDate } = BulkDepreciationCalculationSchema.parse(body);

    const results = [];
    const errors = [];

    for (const assetId of assetIds) {
      try {
        const response = await handleSingleCalculation(assetId, recalculate);
        const responseData = await response.json();

        if (responseData.success) {
          results.push({
            assetId,
            success: true,
            data: responseData.data,
          });
        } else {
          errors.push({
            assetId,
            error: responseData.error,
          });
        }
      } catch (error) {
        errors.push({
          assetId,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        results,
        errors,
        summary: {
          total: assetIds.length,
          successful: results.length,
          failed: errors.length,
        },
      },
    });

  } catch (error) {
    console.error("Error in bulk depreciation calculation:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid bulk calculation parameters",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

// GET /api/depreciation/calculate - Get current book value and depreciation status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assetId = searchParams.get("assetId");

    if (!assetId) {
      return NextResponse.json(
        {
          success: false,
          error: "Asset ID is required"
        },
        { status: 400 }
      );
    }

    // Validate asset ID format
    if (!/^[a-z0-9]+$/i.test(assetId)) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid asset ID format"
        },
        { status: 400 }
      );
    }

    const bookValue = await DepreciationEngine.getCurrentBookValue(assetId);

    // Get additional depreciation info
    const asset = await prisma.asset.findUnique({
      where: { id: assetId },
      include: {
        assetType: {
          include: {
            depreciationSettings: true,
          },
        },
        depreciationSchedule: {
          orderBy: [
            { year: 'desc' },
            { month: 'desc' }
          ],
          take: 1,
        },
      },
    });

    if (!asset) {
      return NextResponse.json(
        {
          success: false,
          error: "Asset not found"
        },
        { status: 404 }
      );
    }

    const latestSchedule = asset.depreciationSchedule[0];
    const accumulatedDepreciation = asset.purchasePrice - bookValue;

    return NextResponse.json({
      success: true,
      data: {
        assetId,
        bookValue,
        accumulatedDepreciation,
        purchasePrice: asset.purchasePrice,
        depreciationMethod: asset.assetType?.depreciationSettings?.method,
        lastCalculated: latestSchedule?.calculatedAt,
        hasDepreciationSettings: !!asset.assetType?.depreciationSettings,
      },
    });

  } catch (error) {
    console.error("Error getting book value:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Internal server error"
      },
      { status: 500 }
    );
  }
}