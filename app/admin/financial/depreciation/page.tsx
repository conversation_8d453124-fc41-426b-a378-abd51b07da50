"use client"

import { use<PERSON>tate, use<PERSON><PERSON><PERSON>, use<PERSON>emo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Progress } from "@/components/ui/progress"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Calculator,
  Calendar,
  ChevronDown,
  Download,
  FileText,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Sliders,
  TrendingDown,
  TrendingUp,
  Upload,
  DollarSign,
  Clock,
  AlertTriangle,
  CheckCircle,
  HelpCircle,
  Info,
  Printer,
  Eye,
  Loader2,
  RefreshCw,
} from "lucide-react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { useHeaderTabs } from "@/hooks/use-admin-tabs"
import { HeaderTabContent, TabContent } from "@/components/ui/header-tab-content"
import { getDepreciationHeaderConfig } from "@/lib/utils/admin-header-configs"
import { getDepreciationHeaderTabs } from "@/lib/utils/admin-tabs-configs"
import { DepreciationService } from "@/lib/services/depreciation-service"
import { toast } from "@/components/ui/use-toast"
import type {
  AssetDepreciationSummary,
  DepreciationCalculationResult,
  DepreciationReportFilter
} from "@/lib/schemas/depreciation"

export default function DepreciationPage() {
  // State management
  const [assetsWithDepreciation, setAssetsWithDepreciation] = useState<any[]>([])
  const [selectedAsset, setSelectedAsset] = useState<any | null>(null)
  const [loading, setLoading] = useState(true)
  const [calculating, setCalculating] = useState(false)
  const [filter, setFilter] = useState<DepreciationReportFilter>({})
  const [statistics, setStatistics] = useState<any>(null)

  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => getDepreciationHeaderConfig(), []);

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getDepreciationHeaderTabs(), []);

  // Set up header-integrated tabs
  const { activeTab } = useHeaderTabs(headerTabs, "overview");

  // Load data on component mount
  useEffect(() => {
    loadDepreciationData();
    loadStatistics();
  }, [filter]);

  const loadDepreciationData = async () => {
    try {
      setLoading(true);
      const result = await DepreciationService.getAssetsWithDepreciation(filter, {
        page: 1,
        limit: 50,
        sortBy: 'name',
        sortOrder: 'asc'
      });
      setAssetsWithDepreciation(result.assets);
    } catch (error) {
      console.error("Error loading depreciation data:", error);
      toast({
        title: "Error",
        description: "Failed to load depreciation data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const stats = await DepreciationService.getDepreciationStatistics(filter);
      setStatistics(stats);
    } catch (error) {
      console.error("Error loading statistics:", error);
    }
  };

  const handleCalculateAll = async () => {
    try {
      setCalculating(true);
      const assetIds = assetsWithDepreciation.map(item => item.asset.id);

      if (assetIds.length === 0) {
        toast({
          title: "No Assets",
          description: "No assets available for calculation",
          variant: "destructive",
        });
        return;
      }

      const result = await DepreciationService.bulkCalculateDepreciation({
        assetIds,
        recalculate: true,
      });

      toast({
        title: "Calculation Complete",
        description: `Successfully calculated depreciation for ${result.summary.successful} assets`,
      });

      // Reload data
      await loadDepreciationData();
    } catch (error) {
      console.error("Error calculating depreciation:", error);
      toast({
        title: "Error",
        description: "Failed to calculate depreciation",
        variant: "destructive",
      });
    } finally {
      setCalculating(false);
    }
  };

  // Function to get tab content based on tab ID
  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "overview":
        return (
          <TabContent>
            <DepreciationOverview
              statistics={statistics}
              assets={assetsWithDepreciation}
              loading={loading}
              onRefresh={loadDepreciationData}
            />
          </TabContent>
        );
      case "calculator":
        return (
          <TabContent>
            <DepreciationCalculator
              assets={assetsWithDepreciation}
              selectedAsset={selectedAsset}
              onAssetSelect={setSelectedAsset}
              calculating={calculating}
              onCalculate={handleCalculateAll}
            />
          </TabContent>
        );
      case "schedules":
        return (
          <TabContent>
            <DepreciationSchedules
              assets={assetsWithDepreciation}
              loading={loading}
            />
          </TabContent>
        );
      case "reports":
        return (
          <TabContent>
            <DepreciationReports
              assets={assetsWithDepreciation}
              statistics={statistics}
              filter={filter}
              onFilterChange={setFilter}
            />
          </TabContent>
        );
      case "settings":
        return (
          <TabContent>
            <DepreciationSettings
              onSettingsChange={loadDepreciationData}
            />
          </TabContent>
        );
      default:
        return <TabContent>Content for {tabId} tab</TabContent>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "calculator", content: getTabContent("calculator") },
          { id: "schedules", content: getTabContent("schedules") },
          { id: "reports", content: getTabContent("reports") },
          { id: "settings", content: getTabContent("settings") },
        ]}
      />
    </div>
  );
}

// Component implementations for each tab
interface DepreciationOverviewProps {
  statistics: any;
  assets: any[];
  loading: boolean;
  onRefresh: () => void;
}

function DepreciationOverview({ statistics, assets, loading, onRefresh }: DepreciationOverviewProps) {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Assets</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.totalAssets || 0}</div>
            <p className="text-xs text-muted-foreground">
              {statistics?.assetsWithDepreciation || 0} with depreciation
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${(statistics?.totalAcquisitionCost || 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Acquisition cost
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Accumulated Depreciation</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${(statistics?.totalAccumulatedDepreciation || 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {statistics?.averageDepreciationRate?.toFixed(1) || 0}% average rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Book Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${(statistics?.totalNetBookValue || 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Current value
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Assets Table */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Recent Assets</CardTitle>
            <CardDescription>Assets with depreciation calculations</CardDescription>
          </div>
          <Button variant="outline" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Asset Name</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Purchase Price</TableHead>
                <TableHead>Current Value</TableHead>
                <TableHead>Depreciation</TableHead>
                <TableHead>Method</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {assets.slice(0, 10).map((item) => (
                <TableRow key={item.asset.id}>
                  <TableCell className="font-medium">{item.asset.name}</TableCell>
                  <TableCell>{item.asset.category}</TableCell>
                  <TableCell>${item.asset.purchasePrice.toLocaleString()}</TableCell>
                  <TableCell>${item.depreciation.currentBookValue.toLocaleString()}</TableCell>
                  <TableCell>${item.depreciation.accumulatedDepreciation.toLocaleString()}</TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {item.depreciation.depreciationMethod.replace('_', ' ')}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

interface DepreciationCalculatorProps {
  assets: any[];
  selectedAsset: any;
  onAssetSelect: (asset: any) => void;
  calculating: boolean;
  onCalculate: () => void;
}

function DepreciationCalculator({
  assets,
  selectedAsset,
  onAssetSelect,
  calculating,
  onCalculate
}: DepreciationCalculatorProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Depreciation Calculator</CardTitle>
          <CardDescription>Calculate depreciation for individual assets or all assets</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <Label htmlFor="asset-select">Select Asset</Label>
              <Select
                value={selectedAsset?.asset?.id || ""}
                onValueChange={(value) => {
                  const asset = assets.find(item => item.asset.id === value);
                  onAssetSelect(asset);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose an asset..." />
                </SelectTrigger>
                <SelectContent>
                  {assets.map((item) => (
                    <SelectItem key={item.asset.id} value={item.asset.id}>
                      {item.asset.name} - {item.asset.category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={onCalculate} disabled={calculating}>
                {calculating ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Calculator className="h-4 w-4 mr-2" />
                )}
                Calculate All
              </Button>
            </div>
          </div>

          {selectedAsset && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Asset Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Name:</span>
                    <span className="font-medium">{selectedAsset.asset.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Category:</span>
                    <span>{selectedAsset.asset.category}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Purchase Price:</span>
                    <span>${selectedAsset.asset.purchasePrice.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Purchase Date:</span>
                    <span>{new Date(selectedAsset.asset.purchaseDate).toLocaleDateString()}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Depreciation Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Method:</span>
                    <Badge variant="secondary">
                      {selectedAsset.depreciation.depreciationMethod.replace('_', ' ')}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Current Value:</span>
                    <span className="font-medium">
                      ${selectedAsset.depreciation.currentBookValue.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Accumulated:</span>
                    <span>${selectedAsset.depreciation.accumulatedDepreciation.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Remaining Life:</span>
                    <span>{selectedAsset.depreciation.remainingUsefulLife.toFixed(1)} years</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Placeholder components for other tabs
function DepreciationSchedules({ assets, loading }: { assets: any[]; loading: boolean }) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Depreciation Schedules</CardTitle>
          <CardDescription>View and manage depreciation schedules for all assets</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="text-center text-muted-foreground">
              Depreciation schedules will be displayed here
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

function DepreciationReports({
  assets,
  statistics,
  filter,
  onFilterChange
}: {
  assets: any[];
  statistics: any;
  filter: DepreciationReportFilter;
  onFilterChange: (filter: DepreciationReportFilter) => void;
}) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Depreciation Reports</CardTitle>
          <CardDescription>Generate and export depreciation reports</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">
            Depreciation reports will be displayed here
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function DepreciationSettings({ onSettingsChange }: { onSettingsChange: () => void }) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Depreciation Settings</CardTitle>
          <CardDescription>Configure global depreciation settings and asset type defaults</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">
            Depreciation settings will be displayed here
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
